<template>
  <div class="homepage">
    <!-- 顶部导航 -->
    <el-header class="header">
      <div class="header-content">
        <!-- <div class="logo">
          <i class="el-icon-s-home"></i>
          <span>智能管理平台</span>
        </div> -->

        <!-- 搜索栏 -->
        <div class="header-search">
          <!-- <el-autocomplete
            v-model="globalSearch"
            :fetch-suggestions="querySearch"
            placeholder="全局搜索..."
            clearable
            prefix-icon="el-icon-search"
            style="width: 300px"
            @select="handleGlobalSearch"
          >
            <template slot-scope="{ item }">
              <div class="search-item">
                <i :class="item.icon"></i>
                <span>{{ item.value }}</span>
              </div>
            </template>
          </el-autocomplete> -->
        </div>

        <div class="header-right">
          <!-- 天气信息 -->
          <div class="weather-info" @click="showWeatherDetail = true">
            <i class="el-icon-sunny"></i>
            <span>{{ weather.temperature }}°C</span>
            <span class="weather-city">{{ weather.city }}</span>
          </div>

          <!-- 消息中心 -->
          <el-popover placement="bottom" width="350" trigger="click">
            <div class="message-center">
              <el-tabs v-model="activeMessageTab">
                <el-tab-pane label="通知" name="notification">
                  <div class="message-list">
                    <div
                      v-for="(msg, index) in notifications"
                      :key="index"
                      class="message-item"
                      :class="{ unread: !msg.read }"
                      @click="markAsRead('notification', index)"
                    >
                      <div class="message-content">
                        <div class="message-title">{{ msg.title }}</div>
                        <div class="message-text">{{ msg.content }}</div>
                        <div class="message-time">{{ msg.time }}</div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="消息" name="message">
                  <div class="message-list">
                    <div
                      v-for="(msg, index) in messages"
                      :key="index"
                      class="message-item"
                      :class="{ unread: !msg.read }"
                      @click="markAsRead('message', index)"
                    >
                      <el-avatar :size="30" :src="msg.avatar"></el-avatar>
                      <div class="message-content">
                        <div class="message-title">{{ msg.sender }}</div>
                        <div class="message-text">{{ msg.content }}</div>
                        <div class="message-time">{{ msg.time }}</div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="待办" name="todo">
                  <div class="message-list">
                    <div
                      v-for="(todo, index) in urgentTodos"
                      :key="index"
                      class="message-item todo-item"
                    >
                      <div class="message-content">
                        <div class="message-title">{{ todo.title }}</div>
                        <div class="message-text">{{ todo.description }}</div>
                        <div class="message-time">
                          <el-tag :type="getPriorityType(todo.priority)" size="mini">
                            {{ todo.priority }}
                          </el-tag>
                          <span>{{ todo.deadline }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
            <el-badge slot="reference" :value="unreadCount" :max="99">
              <el-button icon="el-icon-bell" circle size="small"></el-button>
            </el-badge>
          </el-popover>

          <!-- 全屏切换 -->
          <!-- <el-tooltip content="全屏" placement="bottom">
            <el-button
              :icon="isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'"
              circle
              size="small"
              @click="toggleFullscreen"
            ></el-button>
          </el-tooltip> -->

          <!-- 主题切换 -->
          <!-- <el-tooltip content="切换主题" placement="bottom">
            <el-button
              :icon="isDarkMode ? 'el-icon-sunny' : 'el-icon-moon'"
              circle
              size="small"
              @click="switchTheme"
            ></el-button>
          </el-tooltip> -->

          <!-- 用户菜单 -->
          <!-- <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-avatar :size="32" :src="userInfo.avatar"></el-avatar>
              <span class="username">{{ userInfo.name }}</span>
              <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="profile">
                <i class="el-icon-user"></i> 个人中心
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <i class="el-icon-setting"></i> 系统设置
              </el-dropdown-item>
              <el-dropdown-item command="help">
                <i class="el-icon-question"></i> 帮助中心
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <i class="el-icon-switch-button"></i> 退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
        </div>
      </div>
    </el-header>

    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
      <div class="breadcrumb-content">
        <div class="breadcrumb-left">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>工作台</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="page-actions">
          <el-button-group>
            <el-button size="medium" @click="refreshData">
              <i class="el-icon-refresh"></i> 刷新数据
            </el-button>
            <el-button type="primary" size="medium" @click="showShortcutDialog = true">
              <i class="el-icon-plus"></i> 快速创建
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <el-main class="main-content">
      <!-- 数据统计卡片（增强版） -->
      <div class="stats-section">
        <el-row :gutter="24">
          <el-col
            v-for="(item, index) in statsData"
            :key="index"
            :xs="24"
            :sm="12"
            :md="6"
            :lg="6"
            :xl="6"
          >
            <el-card
              class="stats-card"
              :body-style="{ padding: '20px' }"
              @click.native="viewStatsDetail(item)"
            >
              <div class="stats-content">
                <div class="stats-icon" :style="{ backgroundColor: item.color }">
                  <i :class="item.icon"></i>
                </div>
                <div class="stats-info">
                  <div class="stats-number">{{ item.value }}</div>
                  <div class="stats-label">{{ item.label }}</div>
                  <div class="stats-trend">
                    <i
                      :class="item.trend > 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                      :style="{ color: item.trend > 0 ? '#67C23A' : '#F56C6C' }"
                    ></i>
                    <span :style="{ color: item.trend > 0 ? '#67C23A' : '#F56C6C' }">
                      {{ Math.abs(item.trend) }}%
                    </span>
                    <span class="trend-text"
                      >较{{ item.period }}{{ item.trend > 0 ? '上升' : '下降' }}</span
                    >
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 工作台快捷入口 -->
      <el-row :gutter="24" class="workspace-section">
        <el-col :span="24">
          <el-card>
            <div slot="header" class="card-header">
              <span>工作台</span>
              <el-button type="text" @click="customizeWorkspace">自定义</el-button>
            </div>
            <div class="workspace-grid">
              <div
                v-for="(item, index) in workspaceItems"
                :key="index"
                class="workspace-item"
                :class="{ disabled: item.disabled }"
                @click="handleWorkspaceAction(item)"
              >
                <div class="workspace-icon" :style="{ backgroundColor: item.color }">
                  <i :class="item.icon"></i>
                </div>
                <div class="workspace-label">{{ item.label }}</div>
                <div class="workspace-desc">{{ item.description }}</div>
                <div v-if="item.badge" class="workspace-badge">
                  <el-badge :value="item.badge" :type="item.badgeType || 'primary'"></el-badge>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表和数据分析区域 -->
      <el-row :gutter="24" class="chart-section">
        <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
          <el-card>
            <div slot="header" class="card-header">
              <span>数据分析</span>
              <div class="chart-controls">
                <el-select
                  v-model="selectedMetric"
                  size="small"
                  style="width: 120px; margin-right: 10px"
                >
                  <el-option label="访问量" value="visits"></el-option>
                  <el-option label="销售额" value="sales"></el-option>
                  <el-option label="用户数" value="users"></el-option>
                </el-select>
                <el-radio-group v-model="chartType" size="small">
                  <el-radio-button label="day">今日</el-radio-button>
                  <el-radio-button label="week">本周</el-radio-button>
                  <el-radio-button label="month">本月</el-radio-button>
                  <el-radio-button label="year">本年</el-radio-button>
                </el-radio-group>
                <el-button-group style="margin-left: 10px">
                  <el-button
                    size="small"
                    :type="chartViewType === 'line' ? 'primary' : ''"
                    @click="chartViewType = 'line'"
                  >
                    <i class="el-icon-s-data"></i>
                  </el-button>
                  <el-button
                    size="small"
                    :type="chartViewType === 'bar' ? 'primary' : ''"
                    @click="chartViewType = 'bar'"
                  >
                    <i class="el-icon-s-marketing"></i>
                  </el-button>
                </el-button-group>
              </div>
            </div>
            <div ref="chart" class="chart-container">
              <!-- 模拟图表数据 -->
              <div class="chart-mock">
                <div class="chart-title">
                  {{
                    selectedMetric === 'visits'
                      ? '访问量趋势'
                      : selectedMetric === 'sales'
                        ? '销售趋势'
                        : '用户增长趋势'
                  }}
                </div>
                <div class="chart-data">
                  <div v-for="(point, index) in chartData" :key="index" class="data-point">
                    <div
                      class="data-bar"
                      :style="{ height: point.value + '%', backgroundColor: point.color }"
                    ></div>
                    <div class="data-label">{{ point.label }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 实时监控 -->
          <el-card style="margin-top: 20px">
            <div slot="header" class="card-header">
              <span>实时监控</span>
              <el-switch
                v-model="realTimeMonitoring"
                active-text="开启"
                inactive-text="关闭"
              ></el-switch>
            </div>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="monitor-item">
                  <div class="monitor-title">在线用户</div>
                  <div class="monitor-value">{{ monitoring.onlineUsers }}</div>
                  <div class="monitor-chart">
                    <div
                      v-for="(bar, index) in monitoring.onlineHistory"
                      :key="index"
                      class="mini-bar"
                      :style="{ height: bar + '%' }"
                    ></div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="monitor-item">
                  <div class="monitor-title">实时访问</div>
                  <div class="monitor-value">{{ monitoring.realTimeVisits }}</div>
                  <div class="monitor-chart">
                    <div
                      v-for="(bar, index) in monitoring.visitHistory"
                      :key="index"
                      class="mini-bar"
                      :style="{ height: bar + '%' }"
                    ></div>
                  </div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="monitor-item">
                  <div class="monitor-title">系统负载</div>
                  <div class="monitor-value">{{ monitoring.systemLoad }}%</div>
                  <el-progress
                    :percentage="monitoring.systemLoad"
                    :color="getSystemLoadColor(monitoring.systemLoad)"
                    :show-text="false"
                    style="margin-top: 10px"
                  ></el-progress>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
          <!-- 增强的待办事项 -->
          <el-card class="todo-card">
            <div slot="header" class="card-header">
              <span>我的待办</span>
              <div>
                <el-button type="text" size="small" @click="showTodoFilter = !showTodoFilter">
                  <i class="el-icon-s-operation"></i>
                </el-button>
                <el-button type="primary" size="mini" @click="showTodoDialog = true">
                  <i class="el-icon-plus"></i>
                </el-button>
              </div>
            </div>

            <!-- 筛选器 -->
            <div v-show="showTodoFilter" class="todo-filter">
              <el-select
                v-model="todoFilter"
                size="small"
                style="width: 100%"
                placeholder="筛选待办"
              >
                <el-option label="全部" value="all"></el-option>
                <el-option label="进行中" value="pending"></el-option>
                <el-option label="已完成" value="completed"></el-option>
                <el-option label="高优先级" value="high"></el-option>
              </el-select>
            </div>

            <div class="todo-list">
              <draggable v-model="filteredTodoList" @change="updateTodoOrder">
                <div
                  v-for="todo in filteredTodoList"
                  :key="todo.id"
                  class="todo-item"
                  :class="{
                    completed: todo.completed,
                    overdue: isOverdue(todo.deadline),
                    'high-priority': todo.priority === 'high',
                  }"
                >
                  <div class="todo-main">
                    <el-checkbox v-model="todo.completed" @change="updateTodo(todo.id)">
                      <span class="todo-text">{{ todo.text }}</span>
                    </el-checkbox>
                    <div class="todo-meta">
                      <el-tag
                        v-if="todo.priority !== 'normal'"
                        :type="getPriorityType(todo.priority)"
                        size="mini"
                      >
                        {{ todo.priority }}
                      </el-tag>
                      <span v-if="todo.deadline" class="todo-deadline">
                        <i class="el-icon-time"></i>
                        {{ formatDate(todo.deadline) }}
                      </span>
                    </div>
                  </div>
                  <div class="todo-actions">
                    <el-button type="text" size="mini" @click="editTodo(todo)">
                      <i class="el-icon-edit"></i>
                    </el-button>
                    <el-button type="text" size="mini" @click="deleteTodo(todo.id)">
                      <i class="el-icon-delete"></i>
                    </el-button>
                  </div>
                </div>
              </draggable>
            </div>

            <!-- 进度统计 -->
            <div class="todo-progress">
              <div class="progress-text">
                完成进度: {{ completedTodoCount }}/{{ todoList.length }}
              </div>
              <el-progress
                :percentage="todoCompletionPercentage"
                :stroke-width="6"
                :show-text="false"
              ></el-progress>
            </div>
          </el-card>

          <!-- 日历组件 -->
          <el-card class="calendar-card">
            <div slot="header">
              <span>日程安排</span>
              <el-button type="text" @click="showCalendarDialog = true">查看详情</el-button>
            </div>
            <el-calendar v-model="calendarDate" :range="calendarRange">
              <template slot="dateCell" slot-scope="{ date, data }">
                <div class="calendar-day">
                  <span>{{ data.day.split('-')[2] }}</span>
                  <div v-if="getDayEvents(date).length > 0" class="day-events">
                    <div
                      v-for="(event, index) in getDayEvents(date).slice(0, 2)"
                      :key="index"
                      class="event-dot"
                      :style="{ backgroundColor: event.color }"
                    ></div>
                    <span v-if="getDayEvents(date).length > 2" class="more-events">
                      +{{ getDayEvents(date).length - 2 }}
                    </span>
                  </div>
                </div>
              </template>
            </el-calendar>
          </el-card>

          <!-- 快捷笔记 -->
          <el-card class="notes-card">
            <div slot="header" class="card-header">
              <span>快捷笔记</span>
              <el-button type="text" @click="saveNote">保存</el-button>
            </div>
            <el-input
              v-model="quickNote"
              type="textarea"
              :rows="4"
              placeholder="记录一些想法..."
              @blur="autoSaveNote"
            ></el-input>
            <div class="note-info">
              <span class="save-status">{{ noteSaveStatus }}</span>
              <span class="word-count">{{ quickNote.length }}/500</span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 数据表格和团队动态 -->
      <el-row :gutter="24" class="bottom-section">
        <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
          <el-card>
            <div slot="header" class="card-header">
              <span>项目管理</span>
              <div>
                <el-button-group size="small">
                  <el-button @click="exportData"> <i class="el-icon-download"></i> 导出 </el-button>
                  <el-button @click="importData"> <i class="el-icon-upload2"></i> 导入 </el-button>
                  <el-button type="primary" @click="addProject">
                    <i class="el-icon-plus"></i> 新建项目
                  </el-button>
                </el-button-group>
              </div>
            </div>

            <!-- 表格工具栏 -->
            <div class="table-toolbar">
              <div class="toolbar-left">
                <el-input
                  v-model="searchText"
                  placeholder="搜索项目名称、类型..."
                  size="small"
                  style="width: 250px; margin-right: 10px"
                  prefix-icon="el-icon-search"
                  clearable
                />
                <el-select
                  v-model="statusFilter"
                  size="small"
                  placeholder="状态筛选"
                  style="width: 120px; margin-right: 10px"
                >
                  <el-option label="全部" value=""></el-option>
                  <el-option label="进行中" value="进行中"></el-option>
                  <el-option label="已完成" value="已完成"></el-option>
                  <el-option label="已暂停" value="已暂停"></el-option>
                </el-select>
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  size="small"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 220px"
                >
                </el-date-picker>
              </div>
              <div class="toolbar-right">
                <el-button size="small" @click="refreshTable">
                  <i class="el-icon-refresh"></i>
                </el-button>
                <el-button size="small" @click="showColumnSettings = true">
                  <i class="el-icon-s-tools"></i>
                </el-button>
              </div>
            </div>

            <el-table
              :data="filteredTableData"
              style="width: 100%"
              :default-sort="{ prop: 'date', order: 'descending' }"
              @selection-change="handleSelectionChange"
              @sort-change="handleSortChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="name" label="项目名称" min-width="180" sortable>
                <template slot-scope="scope">
                  <div class="project-name">
                    <el-avatar
                      :size="24"
                      :src="scope.row.avatar"
                      style="margin-right: 8px"
                    ></el-avatar>
                    <span>{{ scope.row.name }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="类型" width="100">
                <template slot-scope="scope">
                  <el-tag :type="getTagType(scope.row.type)" size="small">{{
                    scope.row.type
                  }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="progress" label="进度" width="150">
                <template slot-scope="scope">
                  <div class="progress-cell">
                    <el-progress :percentage="scope.row.progress" :stroke-width="6"></el-progress>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="members" label="团队成员" width="150">
                <template slot-scope="scope">
                  <div class="member-avatars">
                    <el-avatar
                      v-for="(member, index) in scope.row.members.slice(0, 3)"
                      :key="index"
                      :size="24"
                      :src="member.avatar"
                      class="member-avatar"
                    ></el-avatar>
                    <span v-if="scope.row.members.length > 3" class="more-members">
                      +{{ scope.row.members.length - 3 }}
                    </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="date" label="创建日期" width="120" sortable></el-table-column>
              <el-table-column prop="deadline" label="截止日期" width="120">
                <template slot-scope="scope">
                  <span :class="{ overdue: isProjectOverdue(scope.row.deadline) }">
                    {{ scope.row.deadline }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                  <el-tag :type="getStatusType(scope.row.status)" size="small">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template slot-scope="scope">
                  <el-button size="mini" @click="viewProjectDetail(scope.row)">详情</el-button>
                  <el-button size="mini" type="primary" @click="editProject(scope.row)"
                    >编辑</el-button
                  >
                  <el-dropdown
                    trigger="click"
                    @command="(command) => handleProjectAction(command, scope.row)"
                  >
                    <el-button size="mini" type="text">
                      更多<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="copy">复制项目</el-dropdown-item>
                      <el-dropdown-item command="archive">归档</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>

            <!-- 批量操作 -->
            <div v-show="selectedRows.length > 0" class="batch-actions">
              <span>已选择 {{ selectedRows.length }} 项</span>
              <el-button size="small" @click="batchArchive">批量归档</el-button>
              <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
            </div>

            <div class="pagination">
              <el-pagination
                :current-page="currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="filteredTableData.length"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              >
              </el-pagination>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
          <!-- 团队动态 -->
          <el-card class="team-activity">
            <div slot="header">
              <span>团队动态</span>
              <el-button type="text" @click="refreshActivity">刷新</el-button>
            </div>
            <el-timeline>
              <el-timeline-item
                v-for="(activity, index) in teamActivities"
                :key="index"
                :timestamp="activity.timestamp"
                :type="activity.type"
                :icon="activity.icon"
              >
                <div class="activity-content">
                  <div class="activity-user">
                    <el-avatar :size="20" :src="activity.user.avatar"></el-avatar>
                    <span class="user-name">{{ activity.user.name }}</span>
                  </div>
                  <div class="activity-text">{{ activity.content }}</div>
                  <div v-if="activity.extra" class="activity-extra">
                    <el-tag size="mini" :type="activity.extra.type">{{
                      activity.extra.text
                    }}</el-tag>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </el-card>

          <!-- 系统性能监控 -->
          <el-card class="system-monitor">
            <div slot="header" class="card-header">
              <span>系统监控</span>
              <el-switch v-model="autoRefreshMonitor" active-text="自动刷新"></el-switch>
            </div>
            <div class="monitor-grid">
              <div class="monitor-item">
                <div class="monitor-label">CPU使用率</div>
                <div class="monitor-value">{{ systemInfo.cpu }}%</div>
                <el-progress
                  :percentage="systemInfo.cpu"
                  :color="getProgressColor(systemInfo.cpu)"
                  :show-text="false"
                  :stroke-width="8"
                ></el-progress>
              </div>
              <div class="monitor-item">
                <div class="monitor-label">内存使用率</div>
                <div class="monitor-value">{{ systemInfo.memory }}%</div>
                <el-progress
                  :percentage="systemInfo.memory"
                  :color="getProgressColor(systemInfo.memory)"
                  :show-text="false"
                  :stroke-width="8"
                ></el-progress>
              </div>
              <div class="monitor-item">
                <div class="monitor-label">磁盘使用率</div>
                <div class="monitor-value">{{ systemInfo.disk }}%</div>
                <el-progress
                  :percentage="systemInfo.disk"
                  :color="getProgressColor(systemInfo.disk)"
                  :show-text="false"
                  :stroke-width="8"
                ></el-progress>
              </div>
              <div class="monitor-item">
                <div class="monitor-label">网络速度</div>
                <div class="monitor-value">{{ systemInfo.network }}</div>
                <div class="network-detail">
                  <span class="upload">↑ {{ systemInfo.upload }}</span>
                  <span class="download">↓ {{ systemInfo.download }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-main>

    <!-- 对话框组件 -->
    <!-- 天气详情对话框 -->
    <el-dialog title="天气详情" :visible.sync="showWeatherDetail" width="500px">
      <div class="weather-detail">
        <div class="weather-main">
          <div class="weather-temp">{{ weather.temperature }}°C</div>
          <div class="weather-desc">{{ weather.description }}</div>
          <div class="weather-location">
            <i class="el-icon-location"></i>
            {{ weather.city }}
          </div>
        </div>
        <div class="weather-info">
          <div class="info-item">
            <span class="label">湿度:</span>
            <span class="value">{{ weather.humidity }}%</span>
          </div>
          <div class="info-item">
            <span class="label">风速:</span>
            <span class="value">{{ weather.windSpeed }} km/h</span>
          </div>
          <div class="info-item">
            <span class="label">体感温度:</span>
            <span class="value">{{ weather.feelsLike }}°C</span>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 快速创建对话框 -->
    <el-dialog title="快速创建" :visible.sync="showShortcutDialog" width="600px">
      <el-tabs v-model="activeShortcutTab">
        <el-tab-pane label="新建项目" name="project">
          <el-form :model="newProject" label-width="80px">
            <el-form-item label="项目名称">
              <el-input v-model="newProject.name" placeholder="请输入项目名称"></el-input>
            </el-form-item>
            <el-form-item label="项目类型">
              <el-select v-model="newProject.type" placeholder="选择项目类型" style="width: 100%">
                <el-option label="Web开发" value="web"></el-option>
                <el-option label="移动应用" value="mobile"></el-option>
                <el-option label="数据分析" value="data"></el-option>
                <el-option label="运营活动" value="operation"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="项目描述">
              <el-input v-model="newProject.description" type="textarea" :rows="3"></el-input>
            </el-form-item>
            <el-form-item label="截止日期">
              <el-date-picker
                v-model="newProject.deadline"
                type="date"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="新建任务" name="task">
          <el-form :model="newTask" label-width="80px">
            <el-form-item label="任务标题">
              <el-input v-model="newTask.title" placeholder="请输入任务标题"></el-input>
            </el-form-item>
            <el-form-item label="优先级">
              <el-radio-group v-model="newTask.priority">
                <el-radio label="low">低</el-radio>
                <el-radio label="normal">普通</el-radio>
                <el-radio label="high">高</el-radio>
                <el-radio label="urgent">紧急</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="分配给">
              <el-select v-model="newTask.assignee" placeholder="选择负责人" style="width: 100%">
                <el-option
                  v-for="member in teamMembers"
                  :key="member.id"
                  :label="member.name"
                  :value="member.id"
                >
                  <div style="display: flex; align-items: center">
                    <el-avatar
                      :size="20"
                      :src="member.avatar"
                      style="margin-right: 8px"
                    ></el-avatar>
                    <span>{{ member.name }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="任务描述">
              <el-input v-model="newTask.description" type="textarea" :rows="3"></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showShortcutDialog = false">取消</el-button>
        <el-button type="primary" @click="createQuickItem">创建</el-button>
      </div>
    </el-dialog>

    <!-- 待办事项对话框 -->
    <el-dialog
      :title="editingTodo.id ? '编辑待办' : '新建待办'"
      :visible.sync="showTodoDialog"
      width="500px"
    >
      <el-form :model="editingTodo" label-width="80px">
        <el-form-item label="标题" required>
          <el-input v-model="editingTodo.text" placeholder="请输入待办事项"></el-input>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="editingTodo.priority" style="width: 100%">
            <el-option label="普通" value="normal"></el-option>
            <el-option label="重要" value="high"></el-option>
            <el-option label="紧急" value="urgent"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="截止时间">
          <el-date-picker
            v-model="editingTodo.deadline"
            type="datetime"
            placeholder="选择截止时间"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="editingTodo.description"
            type="textarea"
            :rows="3"
            placeholder="详细描述（可选）"
          ></el-input>
        </el-form-item>
        <el-form-item label="标签">
          <el-select
            v-model="editingTodo.tags"
            multiple
            filterable
            allow-create
            placeholder="选择或创建标签"
            style="width: 100%"
          >
            <el-option label="工作" value="work"></el-option>
            <el-option label="学习" value="study"></el-option>
            <el-option label="生活" value="life"></el-option>
            <el-option label="重要" value="important"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showTodoDialog = false">取消</el-button>
        <el-button type="primary" @click="saveTodo">保存</el-button>
      </div>
    </el-dialog>

    <!-- 日历详情对话框 -->
    <el-dialog title="日程管理" :visible.sync="showCalendarDialog" width="800px">
      <div class="calendar-detail">
        <div class="calendar-toolbar">
          <el-button type="primary" size="small" @click="showEventDialog = true">
            <i class="el-icon-plus"></i> 新建日程
          </el-button>
          <div class="view-controls">
            <el-button-group size="small">
              <el-button
                :type="calendarView === 'month' ? 'primary' : ''"
                @click="calendarView = 'month'"
                >月</el-button
              >
              <el-button
                :type="calendarView === 'week' ? 'primary' : ''"
                @click="calendarView = 'week'"
                >周</el-button
              >
              <el-button
                :type="calendarView === 'day' ? 'primary' : ''"
                @click="calendarView = 'day'"
                >日</el-button
              >
            </el-button-group>
          </div>
        </div>
        <el-calendar v-model="calendarDate" :range="calendarRange">
          <template slot="dateCell" slot-scope="{ date, data }">
            <div class="calendar-cell" @click="selectDate(date)">
              <div class="date-number">{{ data.day.split('-')[2] }}</div>
              <div class="events-list">
                <div
                  v-for="(event, index) in getDayEvents(date)"
                  :key="index"
                  class="event-item"
                  :style="{ backgroundColor: event.color }"
                  @click.stop="viewEvent(event)"
                >
                  {{ event.title }}
                </div>
              </div>
            </div>
          </template>
        </el-calendar>
      </div>
    </el-dialog>

    <!-- 悬浮按钮 -->
    <div class="floating-buttons">
      <el-tooltip content="意见反馈" placement="left">
        <el-button
          class="floating-btn feedback-btn"
          circle
          icon="el-icon-chat-dot-round"
          @click="showFeedbackDialog = true"
        ></el-button>
      </el-tooltip>
      <el-tooltip content="返回顶部" placement="left">
        <el-button
          v-show="showBackToTop"
          class="floating-btn top-btn"
          circle
          icon="el-icon-top"
          @click="scrollToTop"
        ></el-button>
      </el-tooltip>
    </div>

    <!-- 意见反馈对话框 -->
    <el-dialog title="意见反馈" :visible.sync="showFeedbackDialog" width="500px">
      <el-form :model="feedback" label-width="80px">
        <el-form-item label="反馈类型">
          <el-radio-group v-model="feedback.type">
            <el-radio label="bug">Bug反馈</el-radio>
            <el-radio label="feature">功能建议</el-radio>
            <el-radio label="other">其他</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="标题">
          <el-input v-model="feedback.title" placeholder="请输入反馈标题"></el-input>
        </el-form-item>
        <el-form-item label="详细描述">
          <el-input
            v-model="feedback.content"
            type="textarea"
            :rows="5"
            placeholder="请详细描述您遇到的问题或建议..."
          ></el-input>
        </el-form-item>
        <el-form-item label="联系方式">
          <el-input v-model="feedback.contact" placeholder="邮箱或电话（可选）"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showFeedbackDialog = false">取消</el-button>
        <el-button type="primary" @click="submitFeedback">提交反馈</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import draggable from 'vuedraggable';

export default {
  name: 'Homepage',
  components: {
    draggable,
  },
  data() {
    return {
      // 全局搜索
      globalSearch: '',
      searchSuggestions: [
        { value: '项目管理', icon: 'el-icon-folder-opened' },
        { value: '用户管理', icon: 'el-icon-user' },
        { value: '数据统计', icon: 'el-icon-s-data' },
        { value: '系统设置', icon: 'el-icon-setting' },
        { value: '订单管理', icon: 'el-icon-s-order' },
      ],

      // 用户信息
      userInfo: {
        name: '张三',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      },

      // 天气信息
      weather: {
        temperature: 25,
        city: '北京',
        description: '晴朗',
        humidity: 65,
        windSpeed: 12,
        feelsLike: 27,
      },
      showWeatherDetail: false,

      // 消息中心
      activeMessageTab: 'notification',
      notifications: [
        {
          title: '系统更新通知',
          content: '系统将于今晚进行例行维护...',
          time: '10分钟前',
          read: false,
        },
        {
          title: '项目审批通过',
          content: '您提交的项目申请已通过审批',
          time: '1小时前',
          read: true,
        },
      ],
      messages: [
        {
          sender: '李四',
          content: '请查收最新的项目文档',
          time: '30分钟前',
          read: false,
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        },
      ],
      urgentTodos: [
        {
          title: '完成季度报告',
          description: '需要在本周五前完成',
          priority: 'high',
          deadline: '2天后',
        },
      ],

      // 主题和界面控制
      isDarkMode: false,
      isFullscreen: false,
      showBackToTop: false,

      // 数据统计
      statsData: [
        {
          label: '今日访问',
          value: '12,340',
          icon: 'el-icon-view',
          color: '#409EFF',
          trend: 12.5,
          period: '昨日',
        },
        {
          label: '本月销售',
          value: '¥234,567',
          icon: 'el-icon-s-goods',
          color: '#67C23A',
          trend: -5.2,
          period: '上月',
        },
        {
          label: '活跃用户',
          value: '8,934',
          icon: 'el-icon-user',
          color: '#E6A23C',
          trend: 8.7,
          period: '上周',
        },
        {
          label: '系统性能',
          value: '99.9%',
          icon: 'el-icon-cpu',
          color: '#F56C6C',
          trend: 0.1,
          period: '昨日',
        },
      ],

      // 工作台
      workspaceItems: [
        {
          label: '项目管理',
          description: '管理所有项目进度',
          icon: 'el-icon-folder-opened',
          color: '#409EFF',
          action: 'projects',
          badge: 5,
        },
        {
          label: '用户管理',
          description: '用户信息与权限',
          icon: 'el-icon-user',
          color: '#67C23A',
          action: 'users',
        },
        {
          label: '订单管理',
          description: '处理客户订单',
          icon: 'el-icon-s-order',
          color: '#E6A23C',
          action: 'orders',
          badge: 12,
          badgeType: 'warning',
        },
        {
          label: '财务报表',
          description: '查看财务数据',
          icon: 'el-icon-s-finance',
          color: '#909399',
          action: 'finance',
        },
        {
          label: '系统设置',
          description: '配置系统参数',
          icon: 'el-icon-setting',
          color: '#F56C6C',
          action: 'settings',
        },
        {
          label: '数据分析',
          description: '深入分析业务数据',
          icon: 'el-icon-s-data',
          color: '#9C27B0',
          action: 'analytics',
          disabled: false,
        },
      ],

      // 图表配置
      chartType: 'week',
      chartViewType: 'line',
      selectedMetric: 'visits',
      chartData: [
        { label: '周一', value: 30, color: '#409EFF' },
        { label: '周二', value: 50, color: '#409EFF' },
        { label: '周三', value: 40, color: '#409EFF' },
        { label: '周四', value: 70, color: '#409EFF' },
        { label: '周五', value: 60, color: '#409EFF' },
        { label: '周六', value: 35, color: '#409EFF' },
        { label: '周日', value: 45, color: '#409EFF' },
      ],

      // 实时监控
      realTimeMonitoring: true,
      monitoring: {
        onlineUsers: 1234,
        realTimeVisits: 567,
        systemLoad: 78,
        onlineHistory: [20, 35, 45, 30, 60, 40, 50, 35, 45, 55],
        visitHistory: [30, 45, 35, 50, 40, 45, 60, 35, 40, 50],
      },

      // 待办事项
      todoList: [
        {
          id: 1,
          text: '完成项目文档',
          completed: false,
          priority: 'high',
          deadline: new Date(Date.now() + 86400000 * 2),
          description: '需要完成技术文档的编写',
          tags: ['工作', '重要'],
        },
        {
          id: 2,
          text: '团队会议准备',
          completed: false,
          priority: 'normal',
          deadline: new Date(Date.now() + 86400000),
          description: '准备下周团队会议的议程',
          tags: ['工作'],
        },
        {
          id: 3,
          text: '代码审查',
          completed: true,
          priority: 'normal',
          deadline: new Date(Date.now() - 86400000),
          description: '审查新功能的代码',
          tags: ['工作'],
        },
      ],
      todoFilter: 'all',
      showTodoFilter: false,
      showTodoDialog: false,
      editingTodo: {
        id: null,
        text: '',
        priority: 'normal',
        deadline: null,
        description: '',
        tags: [],
      },

      // 日历
      calendarDate: new Date(),
      calendarRange: [new Date(), new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)],
      calendarView: 'month',
      showCalendarDialog: false,
      showEventDialog: false,
      events: [
        {
          id: 1,
          title: '产品评审',
          date: new Date(),
          color: '#409EFF',
          type: 'meeting',
        },
        {
          id: 2,
          title: '项目汇报',
          date: new Date(Date.now() + 86400000),
          color: '#67C23A',
          type: 'presentation',
        },
      ],

      // 快捷笔记
      quickNote: '',
      noteSaveStatus: '未保存',

      // 项目表格
      searchText: '',
      statusFilter: '',
      dateRange: null,
      selectedRows: [],
      currentPage: 1,
      pageSize: 10,
      showColumnSettings: false,
      tableData: [
        {
          name: '智能管理系统',
          type: 'Web开发',
          progress: 75,
          members: [
            {
              avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              name: '张三',
            },
            {
              avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              name: '李四',
            },
            {
              avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              name: '王五',
            },
          ],
          date: '2024-01-15',
          deadline: '2024-03-15',
          status: '进行中',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        },
        {
          name: '移动应用开发',
          type: '移动应用',
          progress: 45,
          members: [
            {
              avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              name: '赵六',
            },
            {
              avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              name: '钱七',
            },
          ],
          date: '2024-02-01',
          deadline: '2024-04-01',
          status: '进行中',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        },
        {
          name: '数据分析平台',
          type: '数据分析',
          progress: 100,
          members: [
            {
              avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
              name: '孙八',
            },
          ],
          date: '2023-12-01',
          deadline: '2024-01-31',
          status: '已完成',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        },
      ],

      // 团队动态
      teamActivities: [
        {
          timestamp: '2024-01-20 14:30',
          type: 'success',
          icon: 'el-icon-check',
          user: {
            name: '张三',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          },
          content: '完成了智能管理系统的前端开发',
          extra: {
            type: 'success',
            text: '已完成',
          },
        },
        {
          timestamp: '2024-01-20 12:15',
          type: 'primary',
          icon: 'el-icon-upload',
          user: {
            name: '李四',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          },
          content: '上传了新版本的设计文档',
          extra: {
            type: 'info',
            text: '文档更新',
          },
        },
        {
          timestamp: '2024-01-20 10:00',
          type: 'warning',
          icon: 'el-icon-warning',
          user: {
            name: '王五',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          },
          content: '移动应用项目进度延迟',
          extra: {
            type: 'warning',
            text: '需关注',
          },
        },
      ],

      // 系统监控
      systemInfo: {
        cpu: 65,
        memory: 78,
        disk: 45,
        network: '100 Mbps',
        upload: '2.3 MB/s',
        download: '8.7 MB/s',
      },
      autoRefreshMonitor: true,

      // 对话框控制
      showShortcutDialog: false,
      activeShortcutTab: 'project',
      showFeedbackDialog: false,

      // 表单数据
      newProject: {
        name: '',
        type: '',
        description: '',
        deadline: null,
      },
      newTask: {
        title: '',
        priority: 'normal',
        assignee: '',
        description: '',
      },
      feedback: {
        type: 'bug',
        title: '',
        content: '',
        contact: '',
      },

      // 团队成员
      teamMembers: [
        {
          id: 1,
          name: '张三',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        },
        {
          id: 2,
          name: '李四',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        },
        {
          id: 3,
          name: '王五',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        },
      ],
    };
  },

  computed: {
    // 计算未读消息数量
    unreadCount() {
      const unreadNotifications = this.notifications.filter((n) => !n.read).length;
      const unreadMessages = this.messages.filter((m) => !m.read).length;
      const urgentTodoCount = this.urgentTodos.length;
      return unreadNotifications + unreadMessages + urgentTodoCount;
    },

    // 筛选后的待办列表
    filteredTodoList() {
      let filtered = this.todoList;

      if (this.todoFilter === 'pending') {
        filtered = filtered.filter((todo) => !todo.completed);
      } else if (this.todoFilter === 'completed') {
        filtered = filtered.filter((todo) => todo.completed);
      } else if (this.todoFilter === 'high') {
        filtered = filtered.filter(
          (todo) => todo.priority === 'high' || todo.priority === 'urgent'
        );
      }

      return filtered;
    },

    // 完成的待办数量
    completedTodoCount() {
      return this.todoList.filter((todo) => todo.completed).length;
    },

    // 待办完成百分比
    todoCompletionPercentage() {
      if (this.todoList.length === 0) return 0;
      return Math.round((this.completedTodoCount / this.todoList.length) * 100);
    },

    // 筛选后的表格数据
    filteredTableData() {
      let filtered = this.tableData;

      // 文本搜索
      if (this.searchText) {
        filtered = filtered.filter(
          (item) =>
            item.name.toLowerCase().includes(this.searchText.toLowerCase()) ||
            item.type.toLowerCase().includes(this.searchText.toLowerCase())
        );
      }

      // 状态筛选
      if (this.statusFilter) {
        filtered = filtered.filter((item) => item.status === this.statusFilter);
      }

      // 日期范围筛选
      if (this.dateRange && this.dateRange.length === 2) {
        const [startDate, endDate] = this.dateRange;
        filtered = filtered.filter((item) => {
          const itemDate = new Date(item.date);
          return itemDate >= startDate && itemDate <= endDate;
        });
      }

      return filtered;
    },
  },

  mounted() {
    this.initializeData();
    this.startAutoRefresh();
    this.handleScroll();
    this.loadSavedNote();
  },

  beforeDestroy() {
    this.cleanup();
  },

  methods: {
    // 初始化数据
    initializeData() {
      // 加载用户偏好设置
      this.loadUserPreferences();
      // 获取天气信息
      this.fetchWeatherData();
      // 获取实时数据
      this.fetchRealTimeData();
    },

    // 全局搜索
    querySearch(queryString, callback) {
      const suggestions = this.searchSuggestions.filter((item) =>
        item.value.toLowerCase().includes(queryString.toLowerCase())
      );
      callback(suggestions);
    },

    handleGlobalSearch(item) {
      console.log('搜索:', item.value);
      // 实现全局搜索逻辑
    },

    // 头部菜单处理
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$router.push('/profile');
          break;
        case 'settings':
          this.$router.push('/settings');
          break;
        case 'help':
          window.open('/help', '_blank');
          break;
        case 'logout':
          this.logout();
          break;
      }
    },

    // 消息处理
    markAsRead(type, index) {
      if (type === 'notification') {
        this.notifications[index].read = true;
      } else if (type === 'message') {
        this.messages[index].read = true;
      }
    },

    // 主题切换
    switchTheme() {
      this.isDarkMode = !this.isDarkMode;
      document.documentElement.setAttribute('data-theme', this.isDarkMode ? 'dark' : 'light');
      this.$message.success(`已切换到${this.isDarkMode ? '深色' : '浅色'}主题`);
    },

    // 全屏切换
    toggleFullscreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().then(() => {
          this.isFullscreen = true;
          this.$message.success('已进入全屏模式');
        });
      } else {
        document.exitFullscreen().then(() => {
          this.isFullscreen = false;
          this.$message.success('已退出全屏模式');
        });
      }
    },

    // 工作台功能处理
    handleWorkspaceAction(action) {
      const actionMap = {
        projects: '/projects',
        users: '/users',
        orders: '/orders',
        finance: '/finance',
        settings: '/settings',
        analytics: '/analytics',
        notifications: () => this.showNotificationCenter(),
        reports: '/reports',
      };

      if (typeof actionMap[action] === 'function') {
        actionMap[action]();
      } else if (actionMap[action]) {
        this.$router.push(actionMap[action]);
      }
    },

    // 快捷创建
    createShortcut() {
      this.showShortcutDialog = true;
    },

    createQuickItem() {
      if (this.activeShortcutTab === 'project') {
        this.createProject();
      } else if (this.activeShortcutTab === 'task') {
        this.createTask();
      }
    },

    createProject() {
      if (!this.newProject.name) {
        this.$message.warning('请输入项目名称');
        return;
      }

      // 模拟创建项目
      const project = {
        ...this.newProject,
        id: Date.now(),
        progress: 0,
        status: '计划中',
        date: new Date().toISOString().split('T')[0],
        members: [this.teamMembers[0]],
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      };

      this.tableData.unshift(project);
      this.showShortcutDialog = false;
      this.resetNewProject();
      this.$message.success('项目创建成功');
    },

    createTask() {
      if (!this.newTask.title) {
        this.$message.warning('请输入任务标题');
        return;
      }

      const task = {
        id: Date.now(),
        text: this.newTask.title,
        description: this.newTask.description,
        priority: this.newTask.priority,
        completed: false,
        deadline: null,
        tags: [],
        assignee: this.newTask.assignee,
      };

      this.todoList.unshift(task);
      this.showShortcutDialog = false;
      this.resetNewTask();
      this.$message.success('任务创建成功');
    },

    resetNewProject() {
      this.newProject = {
        name: '',
        type: '',
        description: '',
        deadline: null,
      };
    },

    resetNewTask() {
      this.newTask = {
        title: '',
        priority: 'normal',
        assignee: '',
        description: '',
      };
    },

    // 图表控制
    changeChartType(type) {
      this.chartType = type;
      this.updateChartData();
    },

    changeChartView(type) {
      this.chartViewType = type;
      // 这里可以集成 ECharts 来改变图表视图类型
    },

    updateChartData() {
      // 根据选择的时间范围更新图表数据
      const dataMap = {
        week: [
          { label: '周一', value: 30 },
          { label: '周二', value: 50 },
          { label: '周三', value: 40 },
          { label: '周四', value: 70 },
          { label: '周五', value: 60 },
          { label: '周六', value: 35 },
          { label: '周日', value: 45 },
        ],
        month: [
          { label: '第1周', value: 380 },
          { label: '第2周', value: 420 },
          { label: '第3周', value: 350 },
          { label: '第4周', value: 480 },
        ],
        year: [
          { label: '1月', value: 1200 },
          { label: '2月', value: 1100 },
          { label: '3月', value: 1300 },
          { label: '4月', value: 1400 },
          { label: '5月', value: 1350 },
          { label: '6月', value: 1600 },
        ],
      };

      this.chartData = dataMap[this.chartType] || dataMap.week;
    },

    // 待办事项管理
    addTodo() {
      this.editingTodo = {
        id: null,
        text: '',
        priority: 'normal',
        deadline: null,
        description: '',
        tags: [],
      };
      this.showTodoDialog = true;
    },

    editTodo(todo) {
      this.editingTodo = { ...todo };
      this.showTodoDialog = true;
    },

    saveTodo() {
      if (!this.editingTodo.text) {
        this.$message.warning('请输入待办事项内容');
        return;
      }

      if (this.editingTodo.id) {
        // 编辑现有待办
        const index = this.todoList.findIndex((todo) => todo.id === this.editingTodo.id);
        if (index !== -1) {
          this.todoList.splice(index, 1, { ...this.editingTodo });
          this.$message.success('待办事项更新成功');
        }
      } else {
        // 添加新待办
        const newTodo = {
          ...this.editingTodo,
          id: Date.now(),
          completed: false,
        };
        this.todoList.unshift(newTodo);
        this.$message.success('待办事项添加成功');
      }

      this.showTodoDialog = false;
    },

    toggleTodo(todo) {
      todo.completed = !todo.completed;
      const message = todo.completed ? '任务已完成' : '任务标记为未完成';
      this.$message.success(message);
    },

    deleteTodo(index) {
      this.$confirm('确认删除此待办事项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.todoList.splice(index, 1);
        this.$message.success('删除成功');
      });
    },

    getTodoPriorityClass(priority) {
      const classMap = {
        urgent: 'priority-urgent',
        high: 'priority-high',
        normal: 'priority-normal',
        low: 'priority-low',
      };
      return classMap[priority] || 'priority-normal';
    },

    // 获取优先级标签类型
    getPriorityType(priority) {
      const typeMap = {
        urgent: 'danger',
        high: 'warning',
        normal: 'info',
        low: 'success',
      };
      return typeMap[priority] || 'info';
    },

    // 获取项目类型标签类型
    getTagType(type) {
      const typeMap = {
        Web开发: 'primary',
        移动应用: 'success',
        数据分析: 'warning',
        运营活动: 'info',
      };
      return typeMap[type] || 'info';
    },

    // 获取状态标签类型
    getStatusType(status) {
      const typeMap = {
        进行中: 'primary',
        已完成: 'success',
        已暂停: 'warning',
        计划中: 'info',
      };
      return typeMap[status] || 'info';
    },

    // 获取系统负载颜色
    getSystemLoadColor(load) {
      if (load < 50) return '#67C23A';
      if (load < 80) return '#E6A23C';
      return '#F56C6C';
    },

    // 检查待办事项是否过期
    isOverdue(deadline) {
      if (!deadline) return false;
      const now = new Date();
      const deadlineDate = new Date(deadline);
      return deadlineDate < now;
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      const now = new Date();
      const diffTime = d.getTime() - now.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 0) return '今天';
      if (diffDays === 1) return '明天';
      if (diffDays === -1) return '昨天';
      if (diffDays > 0) return `${diffDays}天后`;
      if (diffDays < 0) return `${Math.abs(diffDays)}天前`;

      return d.toLocaleDateString('zh-CN');
    },

    // 检查项目是否过期
    isProjectOverdue(deadline) {
      if (!deadline) return false;
      const now = new Date();
      const deadlineDate = new Date(deadline);
      return deadlineDate < now;
    },

    // 刷新数据
    refreshData() {
      this.$message.success('数据刷新中...');
      // 重新获取各种数据
      this.fetchWeatherData();
      this.fetchRealTimeData();
      setTimeout(() => {
        this.$message.success('数据刷新完成');
      }, 1000);
    },

    // 自定义工作台
    customizeWorkspace() {
      this.$message.info('工作台自定义功能开发中...');
      // 这里可以实现工作台布局自定义功能
    },

    // 查看统计详情
    viewStatsDetail(item) {
      this.$message.info(`查看${item.label}详情`);
      // 这里可以跳转到详细统计页面或显示详情对话框
    },

    // 更新待办事项
    updateTodo(todoId) {
      const todo = this.todoList.find((t) => t.id === todoId);
      if (todo) {
        const message = todo.completed ? '任务已完成' : '任务标记为未完成';
        this.$message.success(message);
      }
    },

    // 自动保存笔记
    autoSaveNote() {
      if (this.quickNote.trim()) {
        localStorage.setItem('quickNote', this.quickNote);
        this.noteSaveStatus = '自动保存';
        setTimeout(() => {
          this.noteSaveStatus = '未保存';
        }, 2000);
      }
    },

    // 新建项目
    addProject() {
      this.showShortcutDialog = true;
      this.activeShortcutTab = 'project';
    },

    // 查看项目详情
    viewProjectDetail(row) {
      this.$message.info(`查看项目详情: ${row.name}`);
      // 这里可以跳转到项目详情页面或显示详情对话框
    },

    // 处理项目操作
    handleProjectAction(command, row) {
      switch (command) {
        case 'copy':
          this.$message.success(`复制项目: ${row.name}`);
          // 实现项目复制逻辑
          break;
        case 'archive':
          this.$message.success(`归档项目: ${row.name}`);
          // 实现项目归档逻辑
          break;
        case 'delete':
          this.deleteProject(row);
          break;
        default:
          break;
      }
    },

    // 刷新表格
    refreshTable() {
      this.$message.success('表格数据刷新中...');
      // 重新获取表格数据
      setTimeout(() => {
        this.$message.success('表格数据刷新完成');
      }, 1000);
    },

    // 处理每页显示数量变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
    },

    // 处理当前页变化
    handleCurrentChange(val) {
      this.currentPage = val;
    },

    // 处理排序变化
    handleSortChange({ prop, order }) {
      this.$message.info(`按${prop}${order === 'ascending' ? '升序' : '降序'}排序`);
      // 实现排序逻辑
    },

    // 批量归档
    batchArchive() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要归档的项目');
        return;
      }

      this.$confirm(`确认归档选中的 ${this.selectedRows.length} 个项目？`, '批量归档', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.$message.success(`成功归档 ${this.selectedRows.length} 个项目`);
        this.selectedRows = [];
        // 实现批量归档逻辑
      });
    },

    // 导入数据
    importData() {
      this.$message.info('数据导入功能开发中...');
      // 实现数据导入功能
    },

    // 更新待办事项顺序
    updateTodoOrder(evt) {
      if (evt.moved) {
        this.$message.success('待办事项顺序已更新');
        // 这里可以保存新的顺序到后端
      }
    },

    // 刷新团队动态
    refreshActivity() {
      this.$message.success('团队动态刷新中...');
      // 重新获取团队动态数据
      setTimeout(() => {
        this.$message.success('团队动态刷新完成');
      }, 1000);
    },

    // 日历管理
    showCalendar() {
      this.showCalendarDialog = true;
    },

    selectDate(date) {
      this.calendarDate = date;
      console.log('选中日期:', date);
    },

    getDayEvents(date) {
      const dateStr = date.toISOString().split('T')[0];
      return this.events.filter((event) => {
        const eventDateStr = event.date.toISOString().split('T')[0];
        return eventDateStr === dateStr;
      });
    },

    viewEvent(event) {
      this.$alert(event.title, '日程详情', {
        confirmButtonText: '确定',
      });
    },

    // 笔记保存
    saveNote() {
      if (!this.quickNote.trim()) {
        this.$message.warning('笔记内容不能为空');
        return;
      }

      localStorage.setItem('quickNote', this.quickNote);
      this.noteSaveStatus = '已保存';
      this.$message.success('笔记保存成功');

      setTimeout(() => {
        this.noteSaveStatus = '未保存';
      }, 3000);
    },

    loadSavedNote() {
      const savedNote = localStorage.getItem('quickNote');
      if (savedNote) {
        this.quickNote = savedNote;
        this.noteSaveStatus = '已保存';
      }
    },

    // 表格操作
    handleSearch() {
      this.currentPage = 1;
    },

    clearSearch() {
      this.searchText = '';
      this.statusFilter = '';
      this.dateRange = null;
      this.handleSearch();
    },

    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    editProject(row) {
      this.$message.info(`编辑项目: ${row.name}`);
      // 实现编辑逻辑
    },

    deleteProject(row) {
      this.$confirm(`确认删除项目 "${row.name}"？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const index = this.tableData.findIndex((item) => item.name === row.name);
        if (index !== -1) {
          this.tableData.splice(index, 1);
          this.$message.success('删除成功');
        }
      });
    },

    batchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要删除的项目');
        return;
      }

      this.$confirm(`确认删除选中的 ${this.selectedRows.length} 个项目？`, '批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.selectedRows.forEach((row) => {
          const index = this.tableData.findIndex((item) => item.name === row.name);
          if (index !== -1) {
            this.tableData.splice(index, 1);
          }
        });
        this.$message.success(`成功删除 ${this.selectedRows.length} 个项目`);
        this.selectedRows = [];
      });
    },

    exportData() {
      // 实现数据导出功能
      this.$message.success('数据导出中...');
      setTimeout(() => {
        this.$message.success('数据导出完成');
      }, 2000);
    },

    // 系统监控
    getProgressColor(percentage) {
      if (percentage < 50) return '#67C23A';
      if (percentage < 80) return '#E6A23C';
      return '#F56C6C';
    },

    // 实时数据更新
    fetchRealTimeData() {
      // 模拟获取实时数据
      setInterval(() => {
        if (this.realTimeMonitoring) {
          // 更新在线用户数
          this.monitoring.onlineUsers += Math.floor(Math.random() * 20) - 10;
          this.monitoring.onlineUsers = Math.max(0, this.monitoring.onlineUsers);

          // 更新实时访问量
          this.monitoring.realTimeVisits += Math.floor(Math.random() * 10) - 5;
          this.monitoring.realTimeVisits = Math.max(0, this.monitoring.realTimeVisits);

          // 更新系统负载
          this.systemInfo.cpu = Math.max(
            0,
            Math.min(100, this.systemInfo.cpu + (Math.random() - 0.5) * 10)
          );
          this.systemInfo.memory = Math.max(
            0,
            Math.min(100, this.systemInfo.memory + (Math.random() - 0.5) * 8)
          );
          this.systemInfo.disk = Math.max(
            0,
            Math.min(100, this.systemInfo.disk + (Math.random() - 0.5) * 2)
          );
        }
      }, 5000);
    },

    // 自动刷新
    startAutoRefresh() {
      if (this.autoRefreshMonitor) {
        setInterval(() => {
          // 刷新监控数据
          this.refreshMonitorData();
        }, 10000);
      }
    },

    refreshMonitorData() {
      // 模拟刷新监控数据
      this.systemInfo.cpu = Math.floor(Math.random() * 100);
      this.systemInfo.memory = Math.floor(Math.random() * 100);
      this.systemInfo.disk = Math.floor(Math.random() * 100);
    },

    // 天气数据
    fetchWeatherData() {
      // 模拟获取天气数据
      setTimeout(() => {
        this.weather = {
          ...this.weather,
          temperature: Math.floor(Math.random() * 30) + 10,
          humidity: Math.floor(Math.random() * 40) + 40,
          windSpeed: Math.floor(Math.random() * 20) + 5,
        };
      }, 1000);
    },

    // 反馈提交
    submitFeedback() {
      if (!this.feedback.title || !this.feedback.content) {
        this.$message.warning('请填写完整的反馈信息');
        return;
      }

      // 模拟提交反馈
      this.$message.success('反馈提交成功，感谢您的建议');
      this.showFeedbackDialog = false;
      this.resetFeedback();
    },

    resetFeedback() {
      this.feedback = {
        type: 'bug',
        title: '',
        content: '',
        contact: '',
      };
    },

    // 滚动控制
    handleScroll() {
      window.addEventListener('scroll', () => {
        this.showBackToTop = window.scrollY > 300;
      });
    },

    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },

    // 用户偏好设置
    loadUserPreferences() {
      const savedPreferences = localStorage.getItem('userPreferences');
      if (savedPreferences) {
        const preferences = JSON.parse(savedPreferences);
        this.isDarkMode = preferences.darkMode || false;
        this.realTimeMonitoring = preferences.realTimeMonitoring !== false;
        this.autoRefreshMonitor = preferences.autoRefresh !== false;
      }
    },

    saveUserPreferences() {
      const preferences = {
        darkMode: this.isDarkMode,
        realTimeMonitoring: this.realTimeMonitoring,
        autoRefresh: this.autoRefreshMonitor,
      };
      localStorage.setItem('userPreferences', JSON.stringify(preferences));
    },

    // 清理函数
    cleanup() {
      // 清理定时器和事件监听器
      this.saveUserPreferences();
    },

    // 退出登录
    logout() {
      this.$confirm('确认退出登录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        localStorage.clear();
        this.$router.push('/login');
        this.$message.success('退出登录成功');
      });
    },
  },
};
</script>

<style scoped>
/* 基础样式 */
.homepage {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #f8fafc 100%);
}

/* 全局卡片样式优化 */
.el-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 32px;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  gap: 8px;
}

.header-search {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 40px;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s;
}

.weather-info:hover {
  background: rgba(255, 255, 255, 0.2);
}

.weather-city {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification {
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 20px;
  transition: all 0.3s;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.1);
}

.username {
  margin-left: 8px;
  font-size: 14px;
}

/* 消息中心样式 */
.message-center {
  max-height: 400px;
}

.message-list {
  max-height: 300px;
  overflow-y: auto;
}

.message-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.message-item:hover {
  background-color: #f8f9fa;
}

.message-item.unread {
  background-color: #f0f9ff;
  border-left: 3px solid #409eff;
}

.message-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.message-info {
  flex: 1;
}

.message-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.message-text {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.message-time {
  font-size: 11px;
  color: #c0c4cc;
}

.message-sender {
  font-weight: 500;
  color: #409eff;
}

.urgent-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 8px;
}

/* 面包屑导航样式 */
.breadcrumb-container {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px 0;
}

.breadcrumb-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32px;
}

.breadcrumb-left {
  flex: 1;
}

.page-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-actions .el-button-group .el-button {
  padding: 10px 20px;
  font-weight: 500;
  border-radius: 8px;
}

.page-actions .el-button-group .el-button:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.page-actions .el-button-group .el-button:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* 主内容区样式 */
.main-content {
  padding: 32px;
  background: #f5f7fa;
}

.section {
  margin-bottom: 24px;
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 32px;
}

.stats-card {
  transition: all 0.3s;
  cursor: pointer;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.stats-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.stats-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.trend-up {
  color: #67c23a;
}

.trend-down {
  color: #f56c6c;
}

/* 图表区域样式 */
.chart-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.chart-body {
  padding: 20px;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
}

/* 实时监控样式 */
.monitoring-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.monitor-card {
  text-align: center;
}

.monitor-value {
  font-size: 24px;
  font-weight: 700;
  margin: 12px 0;
}

.online-users .monitor-value {
  color: #409eff;
}

.real-visits .monitor-value {
  color: #67c23a;
}

.system-load .monitor-value {
  color: #e6a23c;
}

/* 工作台样式 */
.workspace-section {
  margin-bottom: 32px;
}

.workspace-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.workspace-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.workspace-item {
  background: white;
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #f0f0f0;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.workspace-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: #409eff;
}

.workspace-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.workspace-item.disabled:hover {
  transform: none;
  box-shadow: none;
  border-color: transparent;
}

.workspace-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  margin-bottom: 12px;
}

.workspace-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 6px;
}

.workspace-desc {
  font-size: 13px;
  color: #909399;
  line-height: 1.4;
}

.workspace-badge {
  position: absolute;
  top: 12px;
  right: 12px;
}

/* 待办事项样式 */
.todo-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.todo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.todo-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.todo-completion {
  display: flex;
  align-items: center;
  gap: 8px;
}

.todo-filters {
  display: flex;
  gap: 8px;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.todo-list {
  padding: 0;
  max-height: 400px;
  overflow-y: auto;
}

.todo-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #f8f9fa;
  transition: all 0.3s;
}

.todo-item:hover {
  background-color: #f8f9fa;
}

.todo-item.completed {
  opacity: 0.6;
}

.todo-content {
  flex: 1;
  min-width: 0;
}

.todo-text {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
  color: #909399;
}

.todo-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.todo-priority {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.priority-urgent {
  background: #fef0f0;
  color: #f56c6c;
}

.priority-high {
  background: #fdf6ec;
  color: #e6a23c;
}

.priority-normal {
  background: #f0f9ff;
  color: #409eff;
}

.priority-low {
  background: #f0f9ff;
  color: #909399;
}

.todo-tags {
  display: flex;
  gap: 4px;
}

.todo-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.todo-item:hover .todo-actions {
  opacity: 1;
}

.quick-actions {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
}

/* 日历和笔记样式 */
.calendar-note-section {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 16px;
  margin-bottom: 24px;
}

.calendar-widget {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.calendar-header {
  background: #409eff;
  color: white;
  padding: 20px;
  text-align: center;
}

.current-date {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 4px;
}

.current-day {
  font-size: 14px;
  opacity: 0.9;
}

.quick-calendar {
  padding: 20px;
}

.notes-widget {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0;
}

.save-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
}

.save-status.saved {
  background: #f0f9ff;
  color: #67c23a;
}

.save-status.unsaved {
  background: #fef0f0;
  color: #f56c6c;
}

.notes-content {
  flex: 1;
  padding: 20px;
}

/* 数据表格样式 */
.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  flex-wrap: wrap;
  gap: 16px;
}

.search-filters {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.project-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.member-list {
  display: flex;
  gap: 4px;
}

.member-avatar {
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.member-list .member-avatar:not(:first-child) {
  margin-left: -8px;
}

.status-tag {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.pagination {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #f0f0f0;
}

/* 公告动态样式 */
.announcement-section {
  margin-bottom: 24px;
}

.announcement-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.announcement-header {
  background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
  color: white;
  padding: 20px;
  text-align: center;
}

.announcement-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.announcement-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

.announcement-list {
  padding: 0;
  max-height: 300px;
  overflow-y: auto;
}

.announcement-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid #f8f9fa;
  cursor: pointer;
  transition: all 0.3s;
}

.announcement-item:hover {
  background-color: #f8f9fa;
  transform: translateX(4px);
}

.announcement-icon {
  flex-shrink: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-top: 6px;
}

.announcement-icon.important {
  background: #f56c6c;
}

.announcement-icon.notice {
  background: #409eff;
}

.announcement-icon.news {
  background: #67c23a;
}

.announcement-content {
  flex: 1;
  min-width: 0;
}

.announcement-text {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.announcement-date {
  font-size: 12px;
  color: #909399;
}

/* 动态时间线样式 */
.timeline-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  max-height: 400px;
  overflow-y: auto;
}

.timeline-header {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #303133;
  padding: 20px;
  text-align: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.timeline-content {
  padding: 20px;
}

.timeline-item {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.3s;
}

.timeline-item:hover {
  background: #f8f9fa;
}

.timeline-dot {
  flex-shrink: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-top: 6px;
}

.timeline-dot.create {
  background: #67c23a;
}

.timeline-dot.update {
  background: #409eff;
}

.timeline-dot.delete {
  background: #f56c6c;
}

.timeline-dot.login {
  background: #e6a23c;
}

.timeline-info {
  flex: 1;
}

.timeline-text {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 12px;
  color: #909399;
}

/* 系统监控样式 */
.system-monitor {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.monitor-item {
  text-align: center;
}

.monitor-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.monitor-value {
  font-size: 20px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 12px;
}

.network-detail {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 8px;
  font-size: 12px;
}

.upload {
  color: #67c23a;
}

.download {
  color: #409eff;
}

/* 天气详情对话框样式 */
.weather-detail {
  display: flex;
  gap: 20px;
}

.weather-main {
  flex: 1;
  text-align: center;
}

.weather-temp {
  font-size: 48px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 8px;
}

.weather-desc {
  font-size: 18px;
  color: #606266;
  margin-bottom: 12px;
}

.weather-location {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  color: #909399;
}

.weather-info {
  flex: 1;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  color: #606266;
}

.value {
  font-weight: 500;
  color: #303133;
}

/* 对话框表单样式 */
.dialog-actions {
  text-align: right;
  margin-top: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-row .el-form-item {
  flex: 1;
}

/* 返回顶部按钮样式 */
.back-to-top {
  position: fixed;
  right: 30px;
  bottom: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transition: all 0.3s;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.back-to-top:hover {
  background: #337ecc;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.6);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    padding: 0 24px;
  }

  .breadcrumb-content {
    padding: 0 24px;
  }

  .main-content {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .header-search {
    display: none;
  }

  .breadcrumb-content {
    padding: 0 16px;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .page-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .main-content {
    padding: 16px;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .workspace-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .workspace-grid {
    grid-template-columns: 1fr;
  }

  .calendar-note-section {
    grid-template-columns: 1fr;
  }

  .chat-controls {
    flex-direction: column;
    gap: 8px;
  }

  .table-toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-filters {
    flex-direction: column;
  }

  .table-actions {
    justify-content: center;
  }

  .weather-detail {
    flex-direction: column;
  }

  .form-row {
    flex-direction: column;
  }

  .monitor-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 12px;
  }

  .logo span {
    display: none;
  }

  .main-content {
    padding: 12px;
  }

  .section {
    margin-bottom: 16px;
  }

  .stats-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .stats-icon {
    margin: 0 auto;
  }

  .workspace-item {
    padding: 16px;
  }

  .todo-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .todo-actions {
    opacity: 1;
    align-self: flex-end;
  }
}

/* 暗色主题 */
[data-theme='dark'] .homepage {
  background: #1a1a1a;
  color: #e4e7ed;
}

[data-theme='dark'] .el-card {
  background: #2d2d2d;
  border: 1px solid #404040;
}

[data-theme='dark'] .stats-number {
  color: #e4e7ed;
}

[data-theme='dark'] .workspace-item {
  background: #2d2d2d;
  border: 2px solid #404040;
}

[data-theme='dark'] .workspace-item:hover {
  border-color: #409eff;
}

[data-theme='dark'] .workspace-title {
  color: #e4e7ed;
}

[data-theme='dark'] .todo-item:hover {
  background-color: #404040;
}

[data-theme='dark'] .announcement-item:hover,
[data-theme='dark'] .timeline-item:hover {
  background-color: #404040;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.4s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 卡片悬浮效果 */
.hover-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 加载状态 */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 成功提示样式 */
.success-hint {
  color: #67c23a;
  font-size: 12px;
  margin-left: 8px;
}

.error-hint {
  color: #f56c6c;
  font-size: 12px;
  margin-left: 8px;
}

/* 印刷效果 */
.text-shadow {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.card-shadow {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-shadow-hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 渐变背景 */
.gradient-bg-1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-2 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-3 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-bg-4 {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.gradient-bg-5 {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.gradient-bg-6 {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.gradient-bg-7 {
  background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
}

.gradient-bg-8 {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* 特殊效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.neon-glow {
  box-shadow:
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-online {
  background: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.3);
}

.status-offline {
  background: #909399;
}

.status-busy {
  background: #f56c6c;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.3);
}

.status-away {
  background: #e6a23c;
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);
}

/* 优化细节 */
* {
  box-sizing: border-box;
}

.el-button + .el-button {
  margin-left: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

.section-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #409eff;
  margin-right: 8px;
  vertical-align: middle;
}

/* 最终优化 */
.homepage {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-0 {
  margin-bottom: 0;
}
.mb-8 {
  margin-bottom: 8px;
}
.mb-16 {
  margin-bottom: 16px;
}
.mb-24 {
  margin-bottom: 24px;
}

.mt-0 {
  margin-top: 0;
}
.mt-8 {
  margin-top: 8px;
}
.mt-16 {
  margin-top: 16px;
}
.mt-24 {
  margin-top: 24px;
}
</style>
