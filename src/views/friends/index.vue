<template>
  <div id="app">
    <FriendCircle />
  </div>
</template>

<script>
import FriendCircle from './components/FriendCircle.vue';

export default {
  name: 'App',
  components: {
    FriendCircle,
  },
};
</script>

<style>
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
}
</style>
