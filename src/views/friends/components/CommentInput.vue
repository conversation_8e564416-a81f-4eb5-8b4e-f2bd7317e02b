<template>
  <div class="comment-input">
    <div class="input-container">
      <el-input
        ref="commentInput"
        v-model="inputValue"
        type="textarea"
        :rows="3"
        :placeholder="placeholder"
        class="comment-textarea"
        @input="handleInput"
        @keyup.native="handleKeyup"
      ></el-input>

      <!-- @ 用户选择下拉框 -->
      <div v-if="showMentionList" class="mention-dropdown">
        <div
          v-for="user in filteredUsers"
          :key="user.id"
          class="mention-item"
          @click="selectUser(user)"
        >
          <el-avatar :size="24" :src="user.avatar"></el-avatar>
          <span class="mention-name">{{ user.name }}</span>
        </div>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="input-toolbar">
      <div class="toolbar-left">
        <el-button type="text" class="toolbar-btn" @click="showEmojiPanel = !showEmojiPanel">
          😊
        </el-button>
        <el-button type="text" class="toolbar-btn" @click="insertMention"> @ </el-button>
      </div>

      <div class="toolbar-right">
        <el-button size="small" @click="$emit('cancel')">取消</el-button>
        <el-button
          type="primary"
          size="small"
          :disabled="!inputValue.trim()"
          @click="submitComment"
        >
          发送
        </el-button>
      </div>
    </div>

    <!-- 表情面板 -->
    <div v-if="showEmojiPanel" class="emoji-panel">
      <div class="emoji-grid">
        <span
          v-for="(emoji, key) in emojiMap"
          :key="key"
          class="emoji-item"
          @click="insertEmoji(key)"
        >
          {{ emoji }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import { emojiMap } from '../data/mockData.js';

export default {
  name: 'CommentInput',
  props: {
    users: {
      type: Array,
      default: () => [],
    },
    replyTo: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      inputValue: '',
      showEmojiPanel: false,
      showMentionList: false,
      mentionQuery: '',
      cursorPosition: 0,
      emojiMap,
    };
  },
  computed: {
    placeholder() {
      return this.replyTo ? `回复 ${this.replyTo.userName}` : '写评论...';
    },
    filteredUsers() {
      if (!this.mentionQuery) return this.users.slice(0, 5);
      return this.users
        .filter((user) => user.name.toLowerCase().includes(this.mentionQuery.toLowerCase()))
        .slice(0, 5);
    },
  },
  mounted() {
    this.$refs.commentInput.focus();
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    handleInput(value) {
      this.checkMention(value);
    },
    handleKeyup(event) {
      this.cursorPosition = event.target.selectionStart;

      // ESC 键取消
      if (event.key === 'Escape') {
        this.$emit('cancel');
      }

      // Ctrl + Enter 发送
      if (event.ctrlKey && event.key === 'Enter') {
        this.submitComment();
      }
    },
    checkMention(value) {
      const cursorPos = this.cursorPosition;
      const beforeCursor = value.substring(0, cursorPos);
      const atIndex = beforeCursor.lastIndexOf('@');

      if (atIndex !== -1) {
        const afterAt = beforeCursor.substring(atIndex + 1);
        if (!/\s/.test(afterAt)) {
          this.mentionQuery = afterAt;
          this.showMentionList = true;
          return;
        }
      }

      this.showMentionList = false;
      this.mentionQuery = '';
    },
    selectUser(user) {
      const cursorPos = this.cursorPosition;
      const beforeCursor = this.inputValue.substring(0, cursorPos);
      const afterCursor = this.inputValue.substring(cursorPos);
      const atIndex = beforeCursor.lastIndexOf('@');

      if (atIndex !== -1) {
        const newValue = beforeCursor.substring(0, atIndex) + `@${user.name} ` + afterCursor;
        this.inputValue = newValue;
        this.showMentionList = false;
        this.mentionQuery = '';

        this.$nextTick(() => {
          const newCursorPos = atIndex + user.name.length + 2;
          this.$refs.commentInput.$refs.textarea.setSelectionRange(newCursorPos, newCursorPos);
          this.$refs.commentInput.focus();
        });
      }
    },
    insertMention() {
      const cursorPos = this.cursorPosition;
      const beforeCursor = this.inputValue.substring(0, cursorPos);
      const afterCursor = this.inputValue.substring(cursorPos);

      this.inputValue = beforeCursor + '@' + afterCursor;
      this.showMentionList = true;

      this.$nextTick(() => {
        const newCursorPos = cursorPos + 1;
        this.$refs.commentInput.$refs.textarea.setSelectionRange(newCursorPos, newCursorPos);
        this.$refs.commentInput.focus();
      });
    },
    insertEmoji(emojiKey) {
      const cursorPos = this.cursorPosition;
      const beforeCursor = this.inputValue.substring(0, cursorPos);
      const afterCursor = this.inputValue.substring(cursorPos);

      this.inputValue = beforeCursor + `[${emojiKey}]` + afterCursor;
      this.showEmojiPanel = false;

      this.$nextTick(() => {
        const newCursorPos = cursorPos + emojiKey.length + 2;
        this.$refs.commentInput.$refs.textarea.setSelectionRange(newCursorPos, newCursorPos);
        this.$refs.commentInput.focus();
      });
    },
    submitComment() {
      if (this.inputValue.trim()) {
        this.$emit('submit', {
          content: this.inputValue,
          replyTo: this.replyTo,
        });
        this.inputValue = '';
        this.showEmojiPanel = false;
        this.showMentionList = false;
      }
    },
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.showEmojiPanel = false;
        this.showMentionList = false;
      }
    },
  },
};
</script>

<style scoped>
.comment-input {
  position: relative;
}

.input-container {
  position: relative;
}

.comment-textarea {
  width: 100%;
}

.mention-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.mention-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.mention-item:hover {
  background-color: #f5f7fa;
}

.mention-name {
  margin-left: 8px;
  font-size: 14px;
  color: #333;
}

.input-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-btn {
  font-size: 18px;
  padding: 5px 10px;
  color: #666;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}

.emoji-panel {
  position: absolute;
  bottom: 100%;
  left: 0;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 10px;
  margin-bottom: 10px;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  max-width: 320px;
}

.emoji-item {
  font-size: 24px;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-item:hover {
  background-color: #f5f7fa;
  transform: scale(1.2);
}
</style>
