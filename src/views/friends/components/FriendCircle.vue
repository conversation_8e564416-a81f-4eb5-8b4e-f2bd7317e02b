<template>
  <div class="friend-circle">
    <!-- 头部 -->
    <div class="header">
      <div class="user-profile">
        <el-avatar :size="60" :src="currentUser.avatar"></el-avatar>
        <div class="user-info">
          <h3>{{ currentUser.name }}</h3>
          <p>{{ currentUser.signature }}</p>
        </div>
      </div>
      <el-button type="primary" @click="showPublishDialog = true">
        <i class="el-icon-edit"></i> 发布动态
      </el-button>
    </div>

    <!-- 动态列表 -->
    <div class="posts-container">
      <PostItem
        v-for="post in posts"
        :key="post.id"
        :post="post"
        :users="allUsers"
        @like="handleLike"
        @comment="handleComment"
        @delete="handleDelete"
      />
    </div>

    <!-- 发布对话框 -->
    <PublishDialog :visible.sync="showPublishDialog" :users="allUsers" @publish="handlePublish" />

    <!-- 图片预览 -->
    <ImagePreview
      :visible.sync="previewVisible"
      :images="previewImages"
      :initial-index="previewIndex"
    />
  </div>
</template>

<script>
import PostItem from './PostItem.vue';
import PublishDialog from './PublishDialog.vue';
import ImagePreview from './ImagePreview.vue';
import { mockPosts, currentUser, allUsers } from '../data/mockData.js';

export default {
  name: 'FriendCircle',
  components: {
    PostItem,
    PublishDialog,
    ImagePreview,
  },
  provide() {
    return {
      showImagePreview: (images, index) => {
        this.previewImages = images;
        this.previewIndex = index;
        this.previewVisible = true;
      },
    };
  },
  data() {
    return {
      currentUser,
      allUsers,
      posts: mockPosts,
      showPublishDialog: false,
      previewVisible: false,
      previewImages: [],
      previewIndex: 0,
    };
  },
  methods: {
    handleLike(postId) {
      const post = this.posts.find((p) => p.id === postId);
      if (post) {
        const likedIndex = post.likes.findIndex((like) => like.userId === this.currentUser.id);
        if (likedIndex > -1) {
          post.likes.splice(likedIndex, 1);
        } else {
          post.likes.push({
            userId: this.currentUser.id,
            userName: this.currentUser.name,
            timestamp: new Date().toISOString(),
          });
        }
      }
    },
    handleComment(data) {
      const post = this.posts.find((p) => p.id === data.postId);
      if (post) {
        post.comments.push({
          id: Date.now(),
          userId: this.currentUser.id,
          userName: this.currentUser.name,
          userAvatar: this.currentUser.avatar,
          content: data.content,
          timestamp: new Date().toISOString(),
          replyTo: data.replyTo || null,
        });
      }
    },
    handleDelete(postId) {
      this.$confirm('确定要删除这条动态吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.posts = this.posts.filter((p) => p.id !== postId);
          this.$message.success('删除成功');
        })
        .catch(() => {});
    },
    handlePublish(postData) {
      const newPost = {
        id: Date.now(),
        userId: this.currentUser.id,
        userName: this.currentUser.name,
        userAvatar: this.currentUser.avatar,
        content: postData.content,
        images: postData.images || [],
        location: postData.location || null,
        timestamp: new Date().toISOString(),
        likes: [],
        comments: [],
      };
      this.posts.unshift(newPost);
      this.showPublishDialog = false;
      this.$message.success('发布成功');
    },
  },
};
</script>

<style scoped>
.friend-circle {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background: white;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-profile {
  display: flex;
  align-items: center;
}

.user-info {
  margin-left: 15px;
}

.user-info h3 {
  margin: 0 0 5px 0;
  color: #333;
}

.user-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.posts-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
</style>
