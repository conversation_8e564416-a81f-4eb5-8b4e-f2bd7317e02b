<template>
  <el-dialog
    :visible.sync="dialogVisible"
    fullscreen
    :show-close="false"
    class="image-preview-dialog"
    @click.native="closePreview"
  >
    <div class="preview-container" @click.stop>
      <div class="preview-header">
        <span class="image-counter">{{ currentIndex + 1 }} / {{ images.length }}</span>
        <el-button
          type="text"
          icon="el-icon-close"
          class="close-btn"
          @click="closePreview"
        ></el-button>
      </div>

      <div class="preview-content">
        <el-button
          v-if="images.length > 1"
          type="text"
          icon="el-icon-arrow-left"
          class="nav-btn prev-btn"
          :disabled="currentIndex === 0"
          @click="prevImage"
        ></el-button>

        <div class="image-container">
          <img
            :src="images[currentIndex]"
            :alt="`图片 ${currentIndex + 1}`"
            class="preview-image"
            @load="onImageLoad"
          />
        </div>

        <el-button
          v-if="images.length > 1"
          type="text"
          icon="el-icon-arrow-right"
          class="nav-btn next-btn"
          :disabled="currentIndex === images.length - 1"
          @click="nextImage"
        ></el-button>
      </div>

      <div v-if="images.length > 1" class="preview-thumbnails">
        <div
          v-for="(image, index) in images"
          :key="index"
          class="thumbnail-item"
          :class="{ active: index === currentIndex }"
          @click="currentIndex = index"
        >
          <img :src="image" :alt="`缩略图 ${index + 1}`" />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ImagePreview',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    images: {
      type: Array,
      default: () => [],
    },
    initialIndex: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      currentIndex: 0,
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.currentIndex = this.initialIndex;
        document.addEventListener('keydown', this.handleKeydown);
      } else {
        document.removeEventListener('keydown', this.handleKeydown);
      }
    },
  },
  methods: {
    closePreview() {
      this.dialogVisible = false;
    },
    prevImage() {
      if (this.currentIndex > 0) {
        this.currentIndex--;
      }
    },
    nextImage() {
      if (this.currentIndex < this.images.length - 1) {
        this.currentIndex++;
      }
    },
    handleKeydown(event) {
      switch (event.key) {
        case 'Escape':
          this.closePreview();
          break;
        case 'ArrowLeft':
          this.prevImage();
          break;
        case 'ArrowRight':
          this.nextImage();
          break;
      }
    },
    onImageLoad() {
      // 图片加载完成后的处理
    },
  },
};
</script>

<style scoped>
.image-preview-dialog {
  background: rgba(0, 0, 0, 0.9);
}

.image-preview-dialog :deep(.el-dialog) {
  background: transparent;
  box-shadow: none;
  margin: 0;
}

.image-preview-dialog :deep(.el-dialog__body) {
  padding: 0;
  height: 100vh;
}

.preview-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.preview-header {
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 1000;
}

.image-counter {
  color: white;
  font-size: 16px;
  background: rgba(0, 0, 0, 0.6);
  padding: 8px 16px;
  border-radius: 20px;
}

.close-btn {
  color: white;
  font-size: 24px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.image-container {
  max-width: 90vw;
  max-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 20px;
  transition: all 0.3s;
}

.nav-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  color: white;
}

.nav-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.prev-btn {
  left: 20px;
}

.next-btn {
  right: 20px;
}

.preview-thumbnails {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  background: rgba(0, 0, 0, 0.6);
  padding: 10px;
  border-radius: 25px;
  max-width: 90vw;
  overflow-x: auto;
}

.thumbnail-item {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s;
}

.thumbnail-item.active {
  border-color: #409eff;
}

.thumbnail-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-item:hover {
  transform: scale(1.1);
}
</style>
