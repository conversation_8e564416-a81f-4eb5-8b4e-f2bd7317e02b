<template>
  <div class="post-item">
    <!-- 用户信息 -->
    <div class="post-header">
      <div class="user-info">
        <el-avatar :size="40" :src="post.userAvatar"></el-avatar>
        <div class="user-details">
          <h4>{{ post.userName }}</h4>
          <span class="timestamp">{{ formatTime(post.timestamp) }}</span>
          <span v-if="post.location" class="location">
            <i class="el-icon-location"></i>{{ post.location }}
          </span>
        </div>
      </div>
      <el-dropdown v-if="post.userId === currentUser.id">
        <i class="el-icon-more"></i>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="$emit('delete', post.id)"> 删除 </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 内容 -->
    <div class="post-content">
      <div class="content-text" v-html="formatContent(post.content)"></div>

      <!-- 图片展示 -->
      <div v-if="post.images && post.images.length" class="images-grid">
        <div
          v-for="(image, index) in post.images"
          :key="index"
          class="image-item"
          :class="getImageClass(post.images.length)"
          @click="previewImage(index)"
        >
          <img :src="image" alt="图片" />
        </div>
      </div>
    </div>

    <!-- 互动区域 -->
    <div class="post-actions">
      <div class="action-buttons">
        <el-button type="text" :class="{ liked: isLiked }" @click="$emit('like', post.id)">
          <i class="el-icon-heart"></i>
          {{ post.likes.length > 0 ? post.likes.length : '' }}
        </el-button>
        <el-button type="text" @click="showCommentInput = !showCommentInput">
          <i class="el-icon-chat-dot-round"></i>
          {{ post.comments.length > 0 ? post.comments.length : '' }}
        </el-button>
        <el-button type="text">
          <i class="el-icon-share"></i>
        </el-button>
      </div>
    </div>

    <!-- 点赞列表 -->
    <div v-if="post.likes.length" class="likes-section">
      <i class="el-icon-heart like-icon"></i>
      <span class="likes-text">
        {{ post.likes.map((like) => like.userName).join('、') }}
      </span>
    </div>

    <!-- 评论区域 -->
    <div v-if="post.comments.length" class="comments-section">
      <CommentItem
        v-for="comment in post.comments"
        :key="comment.id"
        :comment="comment"
        :users="users"
        @reply="handleReply"
      />
    </div>

    <!-- 评论输入 -->
    <div v-if="showCommentInput" class="comment-input">
      <CommentInput
        :users="users"
        :reply-to="replyTo"
        @submit="submitComment"
        @cancel="cancelComment"
      />
    </div>
  </div>
</template>

<script>
import CommentItem from './CommentItem.vue';
import CommentInput from './CommentInput.vue';
import { currentUser, emojiMap } from '../data/mockData.js';

export default {
  name: 'PostItem',
  components: {
    CommentItem,
    CommentInput,
  },
  inject: ['showImagePreview'],
  props: {
    post: {
      type: Object,
      required: true,
    },
    users: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      currentUser,
      showCommentInput: false,
      replyTo: null,
    };
  },
  computed: {
    isLiked() {
      return this.post.likes.some((like) => like.userId === this.currentUser.id);
    },
  },
  methods: {
    formatTime(timestamp) {
      const now = new Date();
      const time = new Date(timestamp);
      const diff = now - time;

      if (diff < 60000) return '刚刚';
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
      return `${Math.floor(diff / 86400000)}天前`;
    },
    formatContent(content) {
      let formatted = content;

      // 处理@用户
      formatted = formatted.replace(/@(\w+)/g, (match, username) => {
        const user = this.users.find((u) => u.name === username);
        if (user) {
          return `<span class="mention">@${username}</span>`;
        }
        return match;
      });

      // 处理表情包
      Object.keys(emojiMap).forEach((key) => {
        const regex = new RegExp(
          `\

$$
${key}\
$$

`,
          'g'
        );
        formatted = formatted.replace(regex, `<span class="emoji">${emojiMap[key]}</span>`);
      });

      return formatted;
    },
    getImageClass(count) {
      if (count === 1) return 'single';
      if (count <= 4) return 'grid-2';
      return 'grid-3';
    },
    previewImage(index) {
      this.showImagePreview(this.post.images, index);
    },
    submitComment(data) {
      this.$emit('comment', {
        postId: this.post.id,
        content: data.content,
        replyTo: data.replyTo,
      });
      this.showCommentInput = false;
      this.replyTo = null;
    },
    cancelComment() {
      this.showCommentInput = false;
      this.replyTo = null;
    },
    handleReply(user) {
      this.replyTo = user;
      this.showCommentInput = true;
    },
  },
};
</script>

<style scoped>
.post-item {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-details {
  margin-left: 10px;
}

.user-details h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 16px;
}

.timestamp {
  color: #999;
  font-size: 12px;
  margin-right: 10px;
}

.location {
  color: #666;
  font-size: 12px;
}

.post-content .content-text {
  margin: 0 0 15px 0;
  line-height: 1.6;
  color: #333;
  word-wrap: break-word;
}

.images-grid {
  display: grid;
  gap: 5px;
  margin-bottom: 15px;
}

.image-item.single {
  grid-column: 1 / -1;
}

.images-grid.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.images-grid.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.image-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 5px;
  cursor: pointer;
  transition: transform 0.2s;
}

.image-item img:hover {
  transform: scale(1.05);
}

.post-actions {
  border-top: 1px solid #f0f0f0;
  padding-top: 10px;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
}

.liked {
  color: #ff4757 !important;
}

.likes-section {
  margin: 10px 0;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 5px;
  display: flex;
  align-items: center;
}

.like-icon {
  color: #ff4757;
  margin-right: 5px;
}

.likes-text {
  font-size: 14px;
  color: #666;
}

.comments-section {
  margin-top: 15px;
}

.comment-input {
  margin-top: 15px;
}

/* 全局样式 */
:deep(.mention) {
  color: #409eff;
  font-weight: bold;
  cursor: pointer;
}

:deep(.emoji) {
  font-size: 18px;
  vertical-align: middle;
}
</style>
