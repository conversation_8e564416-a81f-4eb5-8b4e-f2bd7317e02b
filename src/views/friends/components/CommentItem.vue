<template>
  <div class="comment-item">
    <el-avatar :size="30" :src="comment.userAvatar"></el-avatar>
    <div class="comment-content">
      <div class="comment-header">
        <span class="comment-author">{{ comment.userName }}</span>
        <span v-if="comment.replyTo" class="reply-to"> 回复 {{ comment.replyTo.userName }} </span>
      </div>
      <p class="comment-text">{{ comment.content }}</p>
      <div class="comment-footer">
        <span class="comment-time">{{ formatTime(comment.timestamp) }}</span>
        <el-button type="text" size="mini" @click="handleReply"> 回复 </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommentItem',
  props: {
    comment: {
      type: Object,
      required: true,
    },
  },
  methods: {
    formatTime(timestamp) {
      const now = new Date();
      const time = new Date(timestamp);
      const diff = now - time;

      if (diff < 60000) return '刚刚';
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
      return `${Math.floor(diff / 86400000)}天前`;
    },
    handleReply() {
      this.$emit('reply', {
        userId: this.comment.userId,
        userName: this.comment.userName,
      });
    },
  },
};
</script>

<style scoped>
.comment-item {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-content {
  flex: 1;
  margin-left: 10px;
}

.comment-header {
  margin-bottom: 5px;
}

.comment-author {
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.reply-to {
  color: #666;
  font-size: 12px;
  margin-left: 5px;
}

.comment-text {
  margin: 5px 0;
  line-height: 1.4;
  color: #333;
  font-size: 14px;
}

.comment-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-time {
  color: #999;
  font-size: 12px;
}
</style>
