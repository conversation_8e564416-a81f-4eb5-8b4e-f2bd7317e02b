<template>
  <el-dialog title="发布动态" :visible.sync="dialogVisible" width="500px" @close="resetForm">
    <el-form ref="form" :model="form">
      <el-form-item>
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="分享新鲜事..."
          maxlength="500"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item>
        <div class="image-upload">
          <el-upload
            v-model="form.images"
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :on-change="handleImageChange"
            :on-remove="handleImageRemove"
            :limit="9"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
        </div>
      </el-form-item>

      <el-form-item>
        <el-input
          v-model="form.location"
          placeholder="添加位置"
          prefix-icon="el-icon-location"
        ></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" :disabled="!form.content.trim()" @click="handlePublish">
        发布
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'PublishDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: {
        content: '',
        images: [],
        location: '',
      },
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
  },
  methods: {
    handleImageChange(file, fileList) {
      // 模拟图片上传，实际项目中需要上传到服务器
      const reader = new FileReader();
      reader.onload = (e) => {
        this.form.images = fileList.map((f) => {
          if (f.raw) {
            return e.target.result;
          }
          return f.url;
        });
      };
      reader.readAsDataURL(file.raw);
    },
    handleImageRemove(file, fileList) {
      this.form.images = fileList.map((f) => f.url).filter(Boolean);
    },
    handlePublish() {
      if (this.form.content.trim()) {
        this.$emit('publish', { ...this.form });
        this.resetForm();
      }
    },
    resetForm() {
      this.form = {
        content: '',
        images: [],
        location: '',
      };
    },
  },
};
</script>

<style scoped>
.image-upload {
  margin: 10px 0;
}

.dialog-footer {
  text-align: right;
}
</style>
