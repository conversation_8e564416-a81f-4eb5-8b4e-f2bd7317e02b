export const currentUser = {
  id: 1,
  name: '张三',
  avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
  signature: '今天也要开心呀～',
};

export const mockPosts = [
  {
    id: 1,
    userId: 2,
    userName: '李四',
    userAvatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
    content: '今天天气真好，和朋友们一起去爬山，心情超棒！生活就是要这样充满阳光和正能量。',
    images: [
      'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
      'https://fuss10.elemecdn.com/1/8e/aeffeb4de74e2fde4bd74fc7b4486jpeg.jpeg',
      'https://fuss10.elemecdn.com/9/bb/e27858e973f5d7d3904835f46abbdjpeg.jpeg',
    ],
    location: '香山公园',
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    likes: [
      { userId: 1, userName: '张三', timestamp: new Date().toISOString() },
      { userId: 3, userName: '王五', timestamp: new Date().toISOString() },
    ],
    comments: [
      {
        id: 1,
        userId: 1,
        userName: '张三',
        userAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        content: '哇，风景真美！',
        timestamp: new Date(Date.now() - 1800000).toISOString(),
        replyTo: null,
      },
      {
        id: 2,
        userId: 3,
        userName: '王五',
        userAvatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
        content: '下次一起去',
        timestamp: new Date(Date.now() - 900000).toISOString(),
        replyTo: null,
      },
    ],
  },
  {
    id: 2,
    userId: 3,
    userName: '王五',
    userAvatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    content: '今天尝试了新的咖啡店，味道很棒，环境也很舒适。推荐给大家！',
    images: ['https://fuss10.elemecdn.com/2/11/6535bcfb26e4c79b48ddde44f4b6fjpeg.jpeg'],
    location: '星巴克咖啡',
    timestamp: new Date(Date.now() - 7200000).toISOString(),
    likes: [{ userId: 1, userName: '张三', timestamp: new Date().toISOString() }],
    comments: [
      {
        id: 3,
        userId: 1,
        userName: '张三',
        userAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        content: '看起来很不错呢！',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        replyTo: null,
      },
    ],
  },
  {
    id: 3,
    userId: 4,
    userName: '赵六',
    userAvatar: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
    content: '周末的时光就是要这样慢慢享受，读书、喝茶、听音乐。',
    images: [],
    location: null,
    timestamp: new Date(Date.now() - 86400000).toISOString(),
    likes: [
      { userId: 1, userName: '张三', timestamp: new Date().toISOString() },
      { userId: 2, userName: '李四', timestamp: new Date().toISOString() },
      { userId: 3, userName: '王五', timestamp: new Date().toISOString() },
    ],
    comments: [
      {
        id: 4,
        userId: 1,
        userName: '张三',
        userAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        content: '这样的生活真让人向往',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        replyTo: null,
      },
    ],
  },
  {
    id: 4,
    userId: 1,
    userName: '张三',
    userAvatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    content: '今天做了一桌好菜，色香味俱全！厨艺又进步了，开心😊',
    images: [
      'https://fuss10.elemecdn.com/d/e6/c4d93a3805b3ce3f323f7974e6f78jpeg.jpeg',
      'https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg',
      'https://fuss10.elemecdn.com/2/11/6535bcfb26e4c79b48ddde44f4b6fjpeg.jpeg',
      'https://fuss10.elemecdn.com/9/bb/e27858e973f5d7d3904835f46abbdjpeg.jpeg',
    ],
    location: '家',
    timestamp: new Date(Date.now() - 172800000).toISOString(),
    likes: [
      { userId: 2, userName: '李四', timestamp: new Date().toISOString() },
      { userId: 4, userName: '赵六', timestamp: new Date().toISOString() },
    ],
    comments: [
      {
        id: 5,
        userId: 2,
        userName: '李四',
        userAvatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
        content: '哇！看起来好好吃！',
        timestamp: new Date(Date.now() - 86400000).toISOString(),
        replyTo: null,
      },
      {
        id: 6,
        userId: 4,
        userName: '赵六',
        userAvatar: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
        content: '什么时候请我们尝尝？',
        timestamp: new Date(Date.now() - 82800000).toISOString(),
        replyTo: null,
      },
    ],
  },
];
